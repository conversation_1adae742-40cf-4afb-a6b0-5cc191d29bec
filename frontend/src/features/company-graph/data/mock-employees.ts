import type { Edge } from '@xyflow/react'
import type { Employee } from '../models/types'

export const mockEmployees: Employee[] = [
    {
        id: 'ceo-001',
        name: '<PERSON><PERSON><PERSON> Петрова',
        job_title: 'Генеральный директор',
        department_name: 'Руководство',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face',
    },

    {
        id: 'cto-001',
        name: 'Д<PERSON>и<PERSON><PERSON><PERSON> Иван<PERSON>',
        job_title: 'Технический директор',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'cfo-001',
        name: '<PERSON><PERSON><PERSON><PERSON>и<PERSON>нова',
        job_title: '<PERSON><PERSON><PERSON><PERSON><PERSON>овый директор',
        department_name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'cmo-001',
        name: 'Михаил Козлов',
        job_title: 'Директор по маркетингу',
        department_name: 'Маркетинг',
        avatar: 'https://images.unsplash.com/photo-1557862921-37829c790f19?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'chro-001',
        name: 'Светлана Соловьева',
        job_title: 'Директор по персоналу',
        department_name: 'HR',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',
    },

    {
        id: 'dev-head-001',
        name: 'Сергей Волков',
        job_title: 'Руководитель разработки',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'qa-head-001',
        name: 'Ольга Морозова',
        job_title: 'Руководитель QA',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'finance-head-001',
        name: 'Александр Новиков',
        job_title: 'Главный бухгалтер',
        department_name: 'Финансы',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-head-001',
        name: 'Татьяна Лебедева',
        job_title: 'Менеджер по продукту',
        department_name: 'Маркетинг',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'hr-head-001',
        name: 'Ирина Зайцева',
        job_title: 'Руководитель отдела кадров',
        department_name: 'HR',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    },

    {
        id: 'dev-001',
        name: 'Игорь Соколов',
        job_title: 'Senior Frontend Developer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'dev-002',
        name: 'Мария Федорова',
        job_title: 'Senior Backend Developer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'dev-003',
        name: 'Павел Романов',
        job_title: 'DevOps Engineer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'qa-001',
        name: 'Андрей Кузнецов',
        job_title: 'QA Engineer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'finance-001',
        name: 'Наталья Орлова',
        job_title: 'Финансовый аналитик',
        department_name: 'Финансы',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-001',
        name: 'Владимир Попов',
        job_title: 'Маркетинг-аналитик',
        department_name: 'Маркетинг',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'hr-001',
        name: 'Екатерина Виноградова',
        job_title: 'HR-менеджер',
        department_name: 'HR',
        avatar: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-002',
        name: 'Артем Беляев',
        job_title: 'SMM-менеджер',
        department_name: 'Маркетинг',
        avatar: 'https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=150&h=150&fit=crop&crop=face',
    },

    {
        id: 'dev-004',
        name: 'Кирилл Егоров',
        job_title: 'Junior Backend Developer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1547425260-76bc4c40042c?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'qa-002',
        name: 'Алиса Богданова',
        job_title: 'Junior QA Engineer',
        department_name: 'IT',
        avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&h=150&fit=crop&crop=face',
    },
    {
        id: 'marketing-003',
        name: 'Денис Давыдов',
        job_title: 'Стажер-маркетолог',
        department_name: 'Маркетинг',
        avatar: 'https://images.unsplash.com/photo-1628157588553-5eeea00af15c?w=150&h=150&fit=crop&crop=face',
    },
]

export const initialNodes = mockEmployees.map((employee) => {
    return {
        id: employee.id,
        type: 'employee' as const,
        job_title: { x: 0, y: 0 },
        data: { employee },
    }
})

export const initialEdges: Edge[] = [
    { id: 'e-ceo-cto', source: 'ceo-001', target: 'cto-001' },
    { id: 'e-ceo-cfo', source: 'ceo-001', target: 'cfo-001' },
    { id: 'e-ceo-cmo', source: 'ceo-001', target: 'cmo-001' },
    { id: 'e-ceo-chro', source: 'ceo-001', target: 'chro-001' },

    { id: 'e-cto-devhead', source: 'cto-001', target: 'dev-head-001' },
    { id: 'e-cto-qahead', source: 'cto-001', target: 'qa-head-001' },
    { id: 'e-cfo-finhead', source: 'cfo-001', target: 'finance-head-001' },
    { id: 'e-cmo-markhead', source: 'cmo-001', target: 'marketing-head-001' },
    { id: 'e-chro-hrhead', source: 'chro-001', target: 'hr-head-001' },

    { id: 'e-devhead-dev1', source: 'dev-head-001', target: 'dev-001' },
    { id: 'e-devhead-dev2', source: 'dev-head-001', target: 'dev-002' },
    { id: 'e-devhead-dev3', source: 'dev-head-001', target: 'dev-003' },
    { id: 'e-qahead-qa1', source: 'qa-head-001', target: 'qa-001' },
    { id: 'e-finhead-fin1', source: 'finance-head-001', target: 'finance-001' },
    { id: 'e-markhead-mark1', source: 'marketing-head-001', target: 'marketing-001' },
    { id: 'e-markhead-mark2', source: 'marketing-head-001', target: 'marketing-002' },
    { id: 'e-hrhead-hr1', source: 'hr-head-001', target: 'hr-001' },

    { id: 'e-dev2-dev4', source: 'dev-002', target: 'dev-004' }, // Senior Backend -> Junior Backend
    { id: 'e-qa1-qa2', source: 'qa-001', target: 'qa-002' }, // QA -> Junior QA
    { id: 'e-mark1-mark3', source: 'marketing-001', target: 'marketing-003' }, // Analyst -> Intern
]
