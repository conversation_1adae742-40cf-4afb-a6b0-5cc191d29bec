import { memo, useState } from 'react'
import {
    ReactFlow,
    useNodesState,
    useEdgesState,
    type Edge,
    ConnectionLineType,
} from '@xyflow/react'

import { useViewportManagement } from './hooks/use-viewport-management'
import type { EmployeeNode as EmployeeNodeType } from './models/types'
import { EmployeeNode } from './ui/employee-node'
import { useGraphLayout } from './hooks/use-graph-layout'
import { CompanyGraphLoading } from './ui/company-graph-loading'
import {
    Dialog,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/shared/ui/kit/dialog'
import { Button } from '@/shared/ui/kit/button'

const nodeTypes = {
    employee: EmployeeNode,
} as const

interface CompanyGraphFlowProps {
    initialNodes: EmployeeNodeType[]
    initialEdges: Edge[]
    className?: string
}

export const CompanyGraphFlow = memo(function CompanyGraphFlow({
    initialNodes,
    initialEdges,
    className = '',
}: CompanyGraphFlowProps) {
    const { nodes: layoutedNodes, edges: layoutedEdges } = useGraphLayout(
        initialNodes,
        initialEdges,
        { nodeHeight: 150, nodeWidth: 240 },
    )

    const [dialogOpen, setDialogOpen] = useState(false)

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(layoutedNodes)
    const [edges] = useEdgesState(layoutedEdges)

    const { focusOnRootNode, isViewportLoading } = useViewportManagement({
        padding: 5,
        rootNodePositionRatio: 0.7,
    })

    return (
        <Dialog
            open={dialogOpen}
            onOpenChange={setDialogOpen}
        >
            <div
                className={` w-full h-full bg-elevated-background rounded-xl backdrop-blur-xs relative ${className}`}
            >
                {isViewportLoading && <CompanyGraphLoading />}
                {/* TODO Заменить цвета на дизайн токены
                    TODO Имплементировать реальную интерактивность для диалога
                */}
                <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onNodeClick={() => setDialogOpen(true)}
                    nodeTypes={nodeTypes}
                    nodesConnectable={false}
                    proOptions={{ hideAttribution: true }}
                    defaultEdgeOptions={{
                        markerEnd: { type: 'arrow', color: '#768099' },
                        type: ConnectionLineType.SmoothStep,
                        style: { strokeWidth: 1, stroke: '#768099' },
                    }}
                    minZoom={0.5}
                    maxZoom={2}
                    onInit={focusOnRootNode}
                ></ReactFlow>
            </div>
            {/* TODO Использовать элемента из UI kit (Dialog, button) */}
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Фёдор Авдеев</DialogTitle>
                </DialogHeader>
                <div className="flex flex-col">
                    <a>Перед тем, как приступить к заданию</a>
                </div>
                <DialogFooter>
                    <Button
                        className="bg-[#6BA2ED] hover:bg-[#6BA2ED]/90"
                        size={'lg'}
                    >
                        Профиль сотрудника
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
})
