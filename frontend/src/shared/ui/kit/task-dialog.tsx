import * as React from 'react'
import { CheckCircle2Icon } from 'lucide-react'
import { cn } from '@/shared/lib/css'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from './dialog'
import { Button } from './button'

interface TaskItem {
    id: string
    text: string
    completed?: boolean
}

interface TaskDialogProps {
    title: string
    tasks: TaskItem[]
    buttonText: string
    onTaskToggle?: (taskId: string) => void
    onButtonClick?: () => void
    children?: React.ReactNode
}

function TaskDialog({
    title,
    tasks,
    buttonText,
    onTaskToggle,
    onButtonClick,
    children,
}: TaskDialogProps) {
    return (
        <Dialog>
            <DialogTrigger asChild>
                {children}
            </DialogTrigger>
            <DialogContent 
                className="backdrop-blur-[10px] bg-white/50 border-white shadow-[0px_0px_40px_0px_rgba(0,0,0,0.1)] rounded-2xl max-w-md p-6"
                showCloseButton={true}
            >
                <div className="flex flex-col gap-6">
                    <DialogHeader className="text-left">
                        <DialogTitle className="text-2xl font-medium text-[#3e4045] leading-normal">
                            {title}
                        </DialogTitle>
                    </DialogHeader>
                    
                    <div className="flex flex-col gap-3">
                        {tasks.map((task) => (
                            <TaskItem
                                key={task.id}
                                task={task}
                                onToggle={onTaskToggle}
                            />
                        ))}
                    </div>
                    
                    <Button
                        onClick={onButtonClick}
                        className="bg-[#6ba2ed] hover:bg-[#6ba2ed]/90 text-white font-medium text-base h-[46px] rounded-[10px] px-6"
                    >
                        {buttonText}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

interface TaskItemProps {
    task: TaskItem
    onToggle?: (taskId: string) => void
}

function TaskItem({ task, onToggle }: TaskItemProps) {
    const handleClick = () => {
        onToggle?.(task.id)
    }

    return (
        <div 
            className="flex items-center gap-1 cursor-pointer group"
            onClick={handleClick}
        >
            <div className="relative shrink-0 w-5 h-5">
                <CheckCircle2Icon 
                    className={cn(
                        "w-5 h-5 transition-colors",
                        task.completed 
                            ? "text-[#6ba2ed] fill-[#6ba2ed]/20" 
                            : "text-[#969FB6] group-hover:text-[#6ba2ed]/70"
                    )}
                />
            </div>
            <p className={cn(
                "text-sm leading-5 text-[#3e4045] transition-colors",
                task.completed && "line-through opacity-60"
            )}>
                {task.text}
            </p>
        </div>
    )
}

export { TaskDialog, type TaskItem, type TaskDialogProps }
