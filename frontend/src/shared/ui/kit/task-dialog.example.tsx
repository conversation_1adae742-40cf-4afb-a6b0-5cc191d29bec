import * as React from 'react'
import { TaskDialog, type TaskItem } from './task-dialog'
import { Button } from './button'

export function TaskDialogExample() {
    const [tasks, setTasks] = React.useState<TaskItem[]>([
        {
            id: '1',
            text: 'Перед тем, как приступить к заданию',
            completed: false,
        },
        {
            id: '2', 
            text: 'Изучить требования к проекту',
            completed: true,
        },
        {
            id: '3',
            text: 'Подготовить рабочее место',
            completed: false,
        },
    ])

    const handleTaskToggle = (taskId: string) => {
        setTasks(prev => 
            prev.map(task => 
                task.id === taskId 
                    ? { ...task, completed: !task.completed }
                    : task
            )
        )
    }

    const handleButtonClick = () => {
        console.log('Переход к профилю сотрудника')
        // Здесь можно добавить логику перехода
    }

    return (
        <div className="p-8">
            <TaskDialog
                title="Фёдор Авдеев"
                tasks={tasks}
                buttonText="Профиль сотрудника"
                onTaskToggle={handleTaskToggle}
                onButtonClick={handleButtonClick}
            >
                <Button>Открыть диалог с задачами</Button>
            </TaskDialog>
        </div>
    )
}
